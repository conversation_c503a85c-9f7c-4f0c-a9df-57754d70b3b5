Metadata-Version: 2.4
Name: mineru
Version: 2.1.8
Summary: A practical tool for converting PDF to Markdown
License: AGPL-3.0
Project-URL: homepage, https://mineru.net/
Project-URL: documentation, https://opendatalab.github.io/MinerU/
Project-URL: repository, https://github.com/opendatalab/MinerU
Project-URL: issues, https://github.com/opendatalab/MinerU/issues
Keywords: magic-pdf,mineru,MinerU,convert,pdf,markdown
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Requires-Python: <3.14,>=3.10
Description-Content-Type: text/markdown
Requires-Dist: boto3>=1.28.43
Requires-Dist: click>=8.1.7
Requires-Dist: loguru>=0.7.2
Requires-Dist: numpy>=1.21.6
Requires-Dist: pdfminer.six==20250506
Requires-Dist: tqdm>=4.67.1
Requires-Dist: requests
Requires-Dist: httpx
Requires-Dist: pillow>=11.0.0
Requires-Dist: pypdfium2>=4.30.0
Requires-Dist: pypdf>=5.6.0
Requires-Dist: reportlab
Requires-Dist: pdftext>=0.6.2
Requires-Dist: modelscope>=1.26.0
Requires-Dist: huggingface-hub>=0.32.4
Requires-Dist: json-repair>=0.46.2
Requires-Dist: opencv-python>=*********
Requires-Dist: fast-langdetect<0.3.0,>=0.2.3
Provides-Extra: test
Requires-Dist: mineru[core]; extra == "test"
Requires-Dist: pytest; extra == "test"
Requires-Dist: pytest-cov; extra == "test"
Requires-Dist: coverage; extra == "test"
Requires-Dist: beautifulsoup4; extra == "test"
Requires-Dist: fuzzywuzzy; extra == "test"
Provides-Extra: vlm
Requires-Dist: transformers>=4.51.1; extra == "vlm"
Requires-Dist: torch>=2.6.0; extra == "vlm"
Requires-Dist: accelerate>=1.5.1; extra == "vlm"
Requires-Dist: pydantic; extra == "vlm"
Provides-Extra: sglang
Requires-Dist: sglang[all]<0.4.10,>=0.4.7; extra == "sglang"
Provides-Extra: pipeline
Requires-Dist: matplotlib<4,>=3.10; extra == "pipeline"
Requires-Dist: ultralytics<9,>=8.3.48; extra == "pipeline"
Requires-Dist: doclayout_yolo==0.0.4; extra == "pipeline"
Requires-Dist: dill<1,>=0.3.8; extra == "pipeline"
Requires-Dist: rapid_table<2.0.0,>=1.0.5; extra == "pipeline"
Requires-Dist: PyYAML<7,>=6.0.2; extra == "pipeline"
Requires-Dist: ftfy<7,>=6.3.1; extra == "pipeline"
Requires-Dist: openai<2,>=1.70.0; extra == "pipeline"
Requires-Dist: shapely<3,>=2.0.7; extra == "pipeline"
Requires-Dist: pyclipper<2,>=1.3.0; extra == "pipeline"
Requires-Dist: omegaconf<3,>=2.3.0; extra == "pipeline"
Requires-Dist: torch!=2.5.0,!=2.5.1,<3,>=2.2.2; extra == "pipeline"
Requires-Dist: torchvision; extra == "pipeline"
Requires-Dist: transformers!=4.51.0,<5.0.0,>=4.49.0; extra == "pipeline"
Provides-Extra: api
Requires-Dist: fastapi; extra == "api"
Requires-Dist: python-multipart; extra == "api"
Requires-Dist: uvicorn; extra == "api"
Provides-Extra: gradio
Requires-Dist: gradio<6,>=5.34; extra == "gradio"
Requires-Dist: gradio-pdf>=0.0.22; extra == "gradio"
Provides-Extra: core
Requires-Dist: mineru[vlm]; extra == "core"
Requires-Dist: mineru[pipeline]; extra == "core"
Requires-Dist: mineru[api]; extra == "core"
Requires-Dist: mineru[gradio]; extra == "core"
Provides-Extra: all
Requires-Dist: mineru[core]; extra == "all"
Requires-Dist: mineru[sglang]; extra == "all"
Provides-Extra: pipeline-old-linux
Requires-Dist: matplotlib<=3.10.1,>=3.10; extra == "pipeline-old-linux"
Requires-Dist: ultralytics<=8.3.104,>=8.3.48; extra == "pipeline-old-linux"
Requires-Dist: doclayout_yolo==0.0.4; extra == "pipeline-old-linux"
Requires-Dist: dill==0.3.8; extra == "pipeline-old-linux"
Requires-Dist: PyYAML==6.0.2; extra == "pipeline-old-linux"
Requires-Dist: ftfy==6.3.1; extra == "pipeline-old-linux"
Requires-Dist: openai==1.71.0; extra == "pipeline-old-linux"
Requires-Dist: shapely==2.1.0; extra == "pipeline-old-linux"
Requires-Dist: pyclipper==1.3.0.post6; extra == "pipeline-old-linux"
Requires-Dist: omegaconf==2.3.0; extra == "pipeline-old-linux"
Requires-Dist: albumentations==1.4.20; extra == "pipeline-old-linux"
Requires-Dist: rapid_table==1.0.3; extra == "pipeline-old-linux"
Requires-Dist: torch!=2.5.0,!=2.5.1,<3,>=2.2.2; extra == "pipeline-old-linux"
Requires-Dist: torchvision; extra == "pipeline-old-linux"
Requires-Dist: transformers!=4.51.0,<5.0.0,>=4.49.0; extra == "pipeline-old-linux"
