# MinerU 纯Python启动方案

## 🎯 概述

这是一套完全基于标准Python环境的MinerU启动方案，**无需uv框架**，使用标准的pip包管理器，适合所有Python用户。

## 📋 系统要求

- Python 3.8+
- pip (Python包管理器)
- 足够的磁盘空间用于模型存储 (~10GB)

## 🚀 快速开始

### 方式1: 一键启动器（推荐）

```bash
# 下载启动器
python pure_python_mineru.py

# 或者直接使用命令行
python pure_python_mineru.py install     # 安装MinerU
python pure_python_mineru.py download    # 下载模型
python pure_python_mineru.py parse demo.pdf  # 解析PDF
```

### 方式2: 手动安装

```bash
# 1. 升级pip
python -m pip install --upgrade pip

# 2. 安装MinerU基础版
python -m pip install "mineru[core]"

# 3. 安装可选依赖
python -m pip install fastapi uvicorn gradio

# 4. 下载模型
python -m mineru.cli.models_download --model-type all

# 5. 解析PDF
python -c "
from mineru.cli.common import do_parse, read_fn
from pathlib import Path
import os

pdf_path = 'demo.pdf'
output_dir = './output'
os.makedirs(output_dir, exist_ok=True)

do_parse(
    output_dir=output_dir,
    pdf_file_names=[Path(pdf_path).stem],
    pdf_bytes_list=[read_fn(Path(pdf_path))],
    p_lang_list=['ch'],
    backend='pipeline'
)
"
```

## 📁 文件说明

### 核心文件

1. **`pure_python_mineru.py`** - 一键启动器（推荐使用）
   - 交互式菜单界面
   - 自动安装和配置
   - 支持所有功能

2. **`quick_start.py`** - 命令行工具
   - 命令行参数支持
   - 适合脚本调用

3. **`python_startup_examples.py`** - 代码示例
   - 各种启动方式的完整示例
   - 适合开发者参考

4. **`mineru_python_guide.md`** - 详细文档
   - 完整的使用指南
   - 配置参数说明

## 🎮 使用方法

### 交互式启动（最简单）

```bash
python pure_python_mineru.py
```

然后按照菜单提示操作：
```
🚀 MinerU 纯Python启动器
==================================================
1. 检查安装状态
2. 安装MinerU (基础版)
3. 安装MinerU (完整版)
4. 下载模型
5. 解析PDF文件
6. 启动API服务
7. 启动Web界面
0. 退出
==================================================
请选择操作 (0-7):
```

### 命令行启动

```bash
# 检查安装
python pure_python_mineru.py check

# 安装MinerU
python pure_python_mineru.py install

# 下载模型
python pure_python_mineru.py download

# 解析PDF
python pure_python_mineru.py parse demo.pdf

# 启动API服务
python pure_python_mineru.py api

# 启动Web界面
python pure_python_mineru.py web
```

### 直接Python API调用

```python
from mineru.cli.common import do_parse, read_fn
from pathlib import Path
import os

def parse_pdf(pdf_path, output_dir="./output"):
    """解析PDF文件"""
    os.makedirs(output_dir, exist_ok=True)
    
    # 读取PDF
    pdf_bytes = read_fn(Path(pdf_path))
    file_name = Path(pdf_path).stem
    
    # 执行解析
    do_parse(
        output_dir=output_dir,
        pdf_file_names=[file_name],
        pdf_bytes_list=[pdf_bytes],
        p_lang_list=["ch"],          # 语言: ch, en, korean, japan
        backend="pipeline",          # 后端: pipeline, vlm-transformers
        parse_method="auto",         # 方法: auto, txt, ocr
        formula_enable=True,         # 公式识别
        table_enable=True,          # 表格识别
    )

# 使用
parse_pdf("./demo.pdf")
```

## 🔧 配置选项

### 后端选择
- `pipeline`: 传统CV流水线（推荐，兼容性好）
- `vlm-transformers`: VLM模型（需要GPU）
- `vlm-sglang-engine`: 高性能VLM（需要GPU和sglang）

### 语言支持
- `ch`: 中文
- `en`: 英文
- `korean`: 韩文
- `japan`: 日文
- 等84种语言...

### 功能开关
- `formula_enable`: 公式识别
- `table_enable`: 表格识别
- `start_page_id/end_page_id`: 页面范围

## 🌐 服务模式

### 1. API服务模式
```bash
python pure_python_mineru.py api
# 访问: http://127.0.0.1:8000/docs
```

### 2. Web界面模式
```bash
python pure_python_mineru.py web
# 访问: http://127.0.0.1:7860
```

### 3. 客户端调用API
```python
import requests
import base64

def call_api(pdf_path):
    with open(pdf_path, 'rb') as f:
        pdf_b64 = base64.b64encode(f.read()).decode('utf-8')
    
    files = {'files': ('demo.pdf', base64.b64decode(pdf_b64))}
    data = {
        'lang_list': ['ch'],
        'backend': 'pipeline',
        'formula_enable': True,
        'table_enable': True
    }
    
    response = requests.post("http://127.0.0.1:8000/file_parse", files=files, data=data)
    return response.json()

result = call_api("demo.pdf")
```

## 🚨 常见问题

### 1. 安装问题
```bash
# 如果pip安装失败，尝试使用国内镜像
python -m pip install -i https://pypi.tuna.tsinghua.edu.cn/simple mineru[core]
```

### 2. 模型下载问题
```bash
# 使用modelscope源（国内用户）
python -m mineru.cli.models_download --source modelscope
```

### 3. 内存不足
```bash
# 使用CPU模式
export MINERU_DEVICE=cpu
python pure_python_mineru.py parse demo.pdf
```

### 4. 权限问题
```bash
# 确保输出目录有写权限
mkdir -p ./output
chmod 755 ./output
```

## 📊 性能对比

| 模式 | 速度 | 内存占用 | GPU需求 | 推荐场景 |
|------|------|----------|---------|----------|
| pipeline | 中等 | 低 | 否 | 通用场景 |
| vlm-transformers | 慢 | 高 | 是 | 高精度需求 |
| vlm-sglang-engine | 快 | 高 | 是 | 大批量处理 |

## 🔗 相关链接

- [MinerU官方仓库](https://github.com/opendatalab/MinerU)
- [在线体验](https://mineru.net/)
- [官方文档](https://opendatalab.github.io/MinerU/)

## 💡 使用建议

1. **首次使用**: 运行 `python pure_python_mineru.py` 使用交互式菜单
2. **批量处理**: 使用API服务模式
3. **简单测试**: 直接使用Python API调用
4. **生产部署**: 使用Docker容器化部署

## 🎉 总结

这套纯Python方案的优势：
- ✅ 无需uv框架，使用标准pip
- ✅ 交互式菜单，操作简单
- ✅ 支持所有MinerU功能
- ✅ 完整的错误处理和日志
- ✅ 适合所有Python用户

立即开始使用：
```bash
python pure_python_mineru.py
```
