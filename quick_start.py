#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MinerU 快速启动脚本
一键启动MinerU的各种服务模式
"""

import os
import sys
import asyncio
import argparse
from pathlib import Path
from loguru import logger

def setup_environment():
    """设置环境变量和基础配置"""
    # 设置中文日志
    logger.remove()
    logger.add(sys.stderr, format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>")
    
    # 基础环境变量
    os.environ.setdefault('PYTORCH_ENABLE_MPS_FALLBACK', '1')
    os.environ.setdefault('NO_ALBUMENTATIONS_UPDATE', '1')
    
    logger.info("🔧 环境配置完成")

def parse_single_file(pdf_path: str, output_dir: str = "./output", **kwargs):
    """解析单个PDF文件"""
    try:
        from mineru.cli.common import do_parse, read_fn
        
        # 检查文件是否存在
        if not Path(pdf_path).exists():
            logger.error(f"❌ 文件不存在: {pdf_path}")
            return False
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 读取PDF文件
        pdf_bytes = read_fn(Path(pdf_path))
        file_name = Path(pdf_path).stem
        
        logger.info(f"📄 开始解析: {pdf_path}")
        
        # 执行解析
        do_parse(
            output_dir=output_dir,
            pdf_file_names=[file_name],
            pdf_bytes_list=[pdf_bytes],
            p_lang_list=[kwargs.get('lang', 'ch')],
            backend=kwargs.get('backend', 'pipeline'),
            parse_method=kwargs.get('method', 'auto'),
            formula_enable=kwargs.get('formula', True),
            table_enable=kwargs.get('table', True),
            start_page_id=kwargs.get('start_page', 0),
            end_page_id=kwargs.get('end_page', None),
        )
        
        logger.info(f"✅ 解析完成！结果保存在: {output_dir}")
        return True
        
    except Exception as e:
        logger.error(f"❌ 解析失败: {e}")
        return False

def start_api_server(host: str = "127.0.0.1", port: int = 8000, **kwargs):
    """启动FastAPI服务"""
    try:
        import uvicorn
        from mineru.cli.fast_api import app
        
        logger.info(f"🚀 启动API服务: http://{host}:{port}")
        logger.info(f"📖 API文档: http://{host}:{port}/docs")
        logger.info("💡 使用Ctrl+C停止服务")
        
        # 将配置参数存储到应用状态
        app.state.config = kwargs
        
        uvicorn.run(
            "mineru.cli.fast_api:app",
            host=host,
            port=port,
            reload=False,
            log_level="info"
        )
        
    except ImportError:
        logger.error("❌ FastAPI未安装，请运行: pip install fastapi uvicorn")
    except Exception as e:
        logger.error(f"❌ API服务启动失败: {e}")

def start_gradio_app(host: str = "127.0.0.1", port: int = 7860, **kwargs):
    """启动Gradio Web界面"""
    try:
        from mineru.cli.gradio_app import main as gradio_main
        import sys
        
        logger.info(f"🌐 启动Web界面: http://{host}:{port}")
        logger.info("💡 使用Ctrl+C停止服务")
        
        # 设置Gradio参数
        sys.argv = [
            "gradio_app",
            "--host", host,
            "--port", str(port)
        ]
        
        gradio_main()
        
    except ImportError:
        logger.error("❌ Gradio未安装，请运行: pip install gradio")
    except Exception as e:
        logger.error(f"❌ Web界面启动失败: {e}")

def download_models(model_type: str = "all", source: str = "huggingface"):
    """下载模型"""
    try:
        from mineru.cli.models_download import download_models as dl_models
        import sys
        
        logger.info(f"📥 开始下载模型: {model_type} from {source}")
        
        # 设置命令行参数
        sys.argv = [
            "download_models",
            "--model-type", model_type,
            "--source", source
        ]
        
        dl_models()
        logger.info("✅ 模型下载完成！")
        
    except Exception as e:
        logger.error(f"❌ 模型下载失败: {e}")

def check_installation():
    """检查安装状态"""
    logger.info("🔍 检查MinerU安装状态...")
    
    try:
        import mineru
        logger.info(f"✅ MinerU已安装，版本: {mineru.version.__version__}")
    except ImportError:
        logger.error("❌ MinerU未安装，请运行: pip install mineru[core]")
        return False
    
    # 检查可选依赖
    optional_deps = {
        'gradio': 'Web界面支持',
        'fastapi': 'API服务支持', 
        'sglang': 'VLM加速支持',
        'torch': 'GPU加速支持'
    }
    
    for dep, desc in optional_deps.items():
        try:
            __import__(dep)
            logger.info(f"✅ {desc}: 已安装")
        except ImportError:
            logger.warning(f"⚠️ {desc}: 未安装")
    
    return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="MinerU 快速启动工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python quick_start.py parse demo.pdf                    # 解析单个PDF
  python quick_start.py api                               # 启动API服务
  python quick_start.py web                               # 启动Web界面
  python quick_start.py download                          # 下载模型
  python quick_start.py check                             # 检查安装
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 解析命令
    parse_parser = subparsers.add_parser('parse', help='解析PDF文件')
    parse_parser.add_argument('pdf_path', help='PDF文件路径')
    parse_parser.add_argument('-o', '--output', default='./output', help='输出目录')
    parse_parser.add_argument('-l', '--lang', default='ch', help='语言设置')
    parse_parser.add_argument('-b', '--backend', default='pipeline', 
                             choices=['pipeline', 'vlm-transformers', 'vlm-sglang-engine'],
                             help='后端选择')
    parse_parser.add_argument('-m', '--method', default='auto',
                             choices=['auto', 'txt', 'ocr'], help='解析方法')
    parse_parser.add_argument('--no-formula', action='store_true', help='禁用公式识别')
    parse_parser.add_argument('--no-table', action='store_true', help='禁用表格识别')
    parse_parser.add_argument('--start-page', type=int, default=0, help='起始页')
    parse_parser.add_argument('--end-page', type=int, help='结束页')
    
    # API服务命令
    api_parser = subparsers.add_parser('api', help='启动API服务')
    api_parser.add_argument('--host', default='127.0.0.1', help='服务器地址')
    api_parser.add_argument('--port', type=int, default=8000, help='服务器端口')
    
    # Web界面命令
    web_parser = subparsers.add_parser('web', help='启动Web界面')
    web_parser.add_argument('--host', default='127.0.0.1', help='服务器地址')
    web_parser.add_argument('--port', type=int, default=7860, help='服务器端口')
    
    # 下载模型命令
    download_parser = subparsers.add_parser('download', help='下载模型')
    download_parser.add_argument('--type', default='all', help='模型类型')
    download_parser.add_argument('--source', default='huggingface',
                                choices=['huggingface', 'modelscope'], help='模型源')
    
    # 检查安装命令
    subparsers.add_parser('check', help='检查安装状态')
    
    args = parser.parse_args()
    
    # 设置环境
    setup_environment()
    
    if not args.command:
        parser.print_help()
        return
    
    # 执行对应命令
    if args.command == 'parse':
        kwargs = {
            'lang': args.lang,
            'backend': args.backend,
            'method': args.method,
            'formula': not args.no_formula,
            'table': not args.no_table,
            'start_page': args.start_page,
            'end_page': args.end_page,
        }
        parse_single_file(args.pdf_path, args.output, **kwargs)
        
    elif args.command == 'api':
        start_api_server(args.host, args.port)
        
    elif args.command == 'web':
        start_gradio_app(args.host, args.port)
        
    elif args.command == 'download':
        download_models(args.type, args.source)
        
    elif args.command == 'check':
        check_installation()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        logger.info("\n👋 用户中断，程序退出")
    except Exception as e:
        logger.error(f"❌ 程序执行失败: {e}")
        sys.exit(1)
