#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MinerU项目Python启动示例
支持多种启动方式：直接API调用、FastAPI服务、Gradio界面、多GPU服务等
"""

import os
import asyncio
import uvicorn
from pathlib import Path
from loguru import logger

# =============================================================================
# 方式1: 直接使用Python API调用（最简单）
# =============================================================================

def example_direct_api():
    """直接使用MinerU的Python API进行文档解析"""
    from mineru.cli.common import do_parse, read_fn
    
    # 配置参数
    pdf_path = "./demo.pdf"  # 替换为你的PDF路径
    output_dir = "./output"
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # 读取PDF文件
        pdf_bytes = read_fn(Path(pdf_path))
        file_name = Path(pdf_path).stem
        
        # 调用解析函数
        do_parse(
            output_dir=output_dir,
            pdf_file_names=[file_name],
            pdf_bytes_list=[pdf_bytes],
            p_lang_list=["ch"],  # 语言设置
            backend="pipeline",  # 后端选择: pipeline, vlm-transformers, vlm-sglang-engine
            parse_method="auto",  # 解析方法: auto, txt, ocr
            formula_enable=True,  # 启用公式识别
            table_enable=True,   # 启用表格识别
            start_page_id=0,     # 起始页
            end_page_id=None,    # 结束页
        )
        
        logger.info(f"✅ 解析完成！结果保存在: {output_dir}")
        
    except Exception as e:
        logger.error(f"❌ 解析失败: {e}")


# =============================================================================
# 方式2: 异步API调用（支持批量处理）
# =============================================================================

async def example_async_api():
    """使用异步API进行批量文档处理"""
    from mineru.cli.common import aio_do_parse, read_fn
    
    # 批量处理多个PDF文件
    pdf_files = [
        "./demo1.pdf",
        "./demo2.pdf", 
        "./demo3.pdf"
    ]
    
    output_dir = "./batch_output"
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # 准备文件数据
        pdf_file_names = []
        pdf_bytes_list = []
        lang_list = []
        
        for pdf_path in pdf_files:
            if Path(pdf_path).exists():
                pdf_file_names.append(Path(pdf_path).stem)
                pdf_bytes_list.append(read_fn(Path(pdf_path)))
                lang_list.append("ch")
        
        if pdf_bytes_list:
            # 异步批量处理
            await aio_do_parse(
                output_dir=output_dir,
                pdf_file_names=pdf_file_names,
                pdf_bytes_list=pdf_bytes_list,
                p_lang_list=lang_list,
                backend="pipeline",
                parse_method="auto",
                formula_enable=True,
                table_enable=True,
            )
            
            logger.info(f"✅ 批量处理完成！结果保存在: {output_dir}")
        else:
            logger.warning("⚠️ 没有找到有效的PDF文件")
            
    except Exception as e:
        logger.error(f"❌ 批量处理失败: {e}")


# =============================================================================
# 方式3: 启动FastAPI服务
# =============================================================================

def start_fastapi_server(host="127.0.0.1", port=8000):
    """启动MinerU FastAPI服务"""
    from mineru.cli.fast_api import app
    
    logger.info(f"🚀 启动MinerU FastAPI服务: http://{host}:{port}")
    logger.info(f"📖 API文档地址: http://{host}:{port}/docs")
    
    # 启动服务
    uvicorn.run(
        "mineru.cli.fast_api:app",
        host=host,
        port=port,
        reload=False  # 生产环境建议设为False
    )


# =============================================================================
# 方式4: 启动Gradio Web界面
# =============================================================================

def start_gradio_app(host="127.0.0.1", port=7860):
    """启动MinerU Gradio Web界面"""
    try:
        from mineru.cli.gradio_app import main as gradio_main
        
        logger.info(f"🌐 启动MinerU Gradio界面: http://{host}:{port}")
        
        # 设置环境变量
        os.environ['GRADIO_SERVER_NAME'] = host
        os.environ['GRADIO_SERVER_PORT'] = str(port)
        
        # 启动Gradio应用
        gradio_main()
        
    except ImportError:
        logger.error("❌ Gradio未安装，请运行: pip install gradio")
    except Exception as e:
        logger.error(f"❌ Gradio启动失败: {e}")


# =============================================================================
# 方式5: 启动sglang服务器（VLM加速）
# =============================================================================

def start_sglang_server(model_path=None, host="127.0.0.1", port=30000):
    """启动sglang服务器用于VLM加速"""
    try:
        from mineru.cli.vlm_sglang_server import main as sglang_main
        import sys
        
        logger.info(f"⚡ 启动sglang服务器: http://{host}:{port}")
        
        # 构建启动参数
        args = [
            "--host", host,
            "--port", str(port),
        ]
        
        if model_path:
            args.extend(["--model-path", model_path])
        
        # 模拟命令行参数
        sys.argv = ["sglang_server"] + args
        
        # 启动sglang服务
        sglang_main()
        
    except ImportError:
        logger.error("❌ sglang未安装，请运行: pip install sglang[all]")
    except Exception as e:
        logger.error(f"❌ sglang服务启动失败: {e}")


# =============================================================================
# 方式6: 多GPU并行服务（高性能）
# =============================================================================

def start_multi_gpu_server(output_dir="/tmp/mineru_output", port=8000):
    """启动多GPU并行处理服务"""
    try:
        import litserve as ls
        from projects.multi_gpu_v2.server import MinerUAPI
        
        logger.info(f"🔥 启动多GPU服务器: http://127.0.0.1:{port}")
        
        # 创建服务器
        server = ls.LitServer(
            MinerUAPI(output_dir=output_dir),
            accelerator='auto',  # 自动检测GPU
            devices='auto',      # 使用所有可用GPU
            workers_per_device=1, # 每个GPU的工作进程数
            timeout=False        # 禁用超时
        )
        
        # 启动服务
        server.run(port=port, generate_client_file=False)
        
    except ImportError:
        logger.error("❌ litserve未安装，请运行: pip install litserve")
    except Exception as e:
        logger.error(f"❌ 多GPU服务启动失败: {e}")


# =============================================================================
# 方式7: 模型下载工具
# =============================================================================

def download_models(model_type="all", source="huggingface"):
    """下载MinerU所需的模型"""
    try:
        from mineru.cli.models_download import download_models as dl_models
        import sys
        
        logger.info(f"📥 开始下载模型: {model_type} from {source}")
        
        # 模拟命令行参数
        sys.argv = [
            "download_models",
            "--model-type", model_type,
            "--source", source
        ]
        
        # 执行下载
        dl_models()
        
        logger.info("✅ 模型下载完成！")
        
    except Exception as e:
        logger.error(f"❌ 模型下载失败: {e}")


# =============================================================================
# 主函数 - 选择启动方式
# =============================================================================

def main():
    """主函数 - 根据需要选择启动方式"""
    
    print("🚀 MinerU Python启动选项:")
    print("1. 直接API调用 (单文件解析)")
    print("2. 异步API调用 (批量处理)")
    print("3. FastAPI服务 (RESTful API)")
    print("4. Gradio Web界面 (用户友好)")
    print("5. sglang服务器 (VLM加速)")
    print("6. 多GPU服务器 (高性能)")
    print("7. 下载模型")
    print("0. 退出")
    
    try:
        choice = input("\n请选择启动方式 (0-7): ").strip()
        
        if choice == "1":
            logger.info("🔧 启动直接API调用...")
            example_direct_api()
            
        elif choice == "2":
            logger.info("🔧 启动异步API调用...")
            asyncio.run(example_async_api())
            
        elif choice == "3":
            logger.info("🔧 启动FastAPI服务...")
            start_fastapi_server()
            
        elif choice == "4":
            logger.info("🔧 启动Gradio界面...")
            start_gradio_app()
            
        elif choice == "5":
            logger.info("🔧 启动sglang服务器...")
            start_sglang_server()
            
        elif choice == "6":
            logger.info("🔧 启动多GPU服务器...")
            start_multi_gpu_server()
            
        elif choice == "7":
            logger.info("🔧 开始下载模型...")
            download_models()
            
        elif choice == "0":
            logger.info("👋 退出程序")
            
        else:
            logger.warning("⚠️ 无效选择，请重新运行")
            
    except KeyboardInterrupt:
        logger.info("\n👋 用户中断，退出程序")
    except Exception as e:
        logger.error(f"❌ 程序执行失败: {e}")


if __name__ == "__main__":
    main()
