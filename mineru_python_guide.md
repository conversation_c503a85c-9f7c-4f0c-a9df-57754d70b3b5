# MinerU Python启动完整指南

## 🎯 概述

MinerU提供了多种Python启动方式，从简单的API调用到完整的Web服务，满足不同场景的需求。

## 📋 前置准备

### 1. 安装MinerU
```bash
# 基础安装
pip install --upgrade pip
pip install uv
uv pip install -U "mineru[core]"

# 如果需要sglang加速
uv pip install -U "mineru[all]"
```

### 2. 配置文件设置
在用户目录下创建 `mineru.json` 配置文件：

```json
{
    "bucket_info":{
        "bucket-name-1":["ak", "sk", "endpoint"]
    },
    "latex-delimiter-config": {
        "display": {"left": "$$", "right": "$$"},
        "inline": {"left": "$", "right": "$"}
    },
    "models-dir": {
        "pipeline": "/path/to/models",
        "vlm": "/path/to/vlm/models"
    },
    "config_version": "1.3.0"
}
```

### 3. 下载模型
```python
from mineru.cli.models_download import download_models
download_models()  # 下载所有必需模型
```

## 🚀 启动方式详解

### 方式1: 直接Python API调用

**适用场景**: 简单的单文件或少量文件处理

```python
from mineru.cli.common import do_parse, read_fn
from pathlib import Path
import os

def parse_single_pdf(pdf_path, output_dir="./output"):
    """解析单个PDF文件"""
    os.makedirs(output_dir, exist_ok=True)
    
    # 读取PDF
    pdf_bytes = read_fn(Path(pdf_path))
    file_name = Path(pdf_path).stem
    
    # 执行解析
    do_parse(
        output_dir=output_dir,
        pdf_file_names=[file_name],
        pdf_bytes_list=[pdf_bytes],
        p_lang_list=["ch"],          # 语言: ch, en, korean, japan等
        backend="pipeline",          # 后端: pipeline, vlm-transformers, vlm-sglang-engine
        parse_method="auto",         # 方法: auto, txt, ocr
        formula_enable=True,         # 公式识别
        table_enable=True,          # 表格识别
        start_page_id=0,            # 起始页
        end_page_id=None,           # 结束页(None表示全部)
    )

# 使用示例
parse_single_pdf("./demo.pdf")
```

### 方式2: 异步批量处理

**适用场景**: 大量文件的批量处理

```python
import asyncio
from mineru.cli.common import aio_do_parse, read_fn
from pathlib import Path

async def batch_parse_pdfs(pdf_paths, output_dir="./batch_output"):
    """批量异步解析PDF文件"""
    os.makedirs(output_dir, exist_ok=True)
    
    # 准备数据
    pdf_file_names = []
    pdf_bytes_list = []
    lang_list = []
    
    for pdf_path in pdf_paths:
        if Path(pdf_path).exists():
            pdf_file_names.append(Path(pdf_path).stem)
            pdf_bytes_list.append(read_fn(Path(pdf_path)))
            lang_list.append("ch")
    
    # 异步批量处理
    await aio_do_parse(
        output_dir=output_dir,
        pdf_file_names=pdf_file_names,
        pdf_bytes_list=pdf_bytes_list,
        p_lang_list=lang_list,
        backend="pipeline",
        parse_method="auto",
        formula_enable=True,
        table_enable=True,
    )

# 使用示例
pdf_files = ["./doc1.pdf", "./doc2.pdf", "./doc3.pdf"]
asyncio.run(batch_parse_pdfs(pdf_files))
```

### 方式3: FastAPI RESTful服务

**适用场景**: 提供HTTP API服务，支持远程调用

```python
# 启动服务
import uvicorn
from mineru.cli.fast_api import app

def start_api_server(host="127.0.0.1", port=8000):
    print(f"🚀 MinerU API服务: http://{host}:{port}")
    print(f"📖 API文档: http://{host}:{port}/docs")
    
    uvicorn.run(
        "mineru.cli.fast_api:app",
        host=host,
        port=port,
        reload=False
    )

start_api_server()
```

**客户端调用示例**:
```python
import requests
import base64

def call_api(pdf_path, server_url="http://127.0.0.1:8000"):
    # 读取并编码PDF文件
    with open(pdf_path, 'rb') as f:
        pdf_b64 = base64.b64encode(f.read()).decode('utf-8')
    
    # 构建请求
    files = {'files': ('demo.pdf', base64.b64decode(pdf_b64))}
    data = {
        'output_dir': './api_output',
        'lang_list': ['ch'],
        'backend': 'pipeline',
        'formula_enable': True,
        'table_enable': True
    }
    
    # 发送请求
    response = requests.post(f"{server_url}/file_parse", files=files, data=data)
    return response.json()

# 使用示例
result = call_api("./demo.pdf")
print(result)
```

### 方式4: Gradio Web界面

**适用场景**: 用户友好的Web界面，适合非技术用户

```python
def start_gradio_app():
    """启动Gradio Web界面"""
    from mineru.cli.gradio_app import main as gradio_main
    
    print("🌐 启动Gradio界面: http://127.0.0.1:7860")
    gradio_main()

start_gradio_app()
```

### 方式5: sglang VLM加速服务

**适用场景**: 需要高性能VLM推理的场景

```python
def start_sglang_server(model_path=None, port=30000):
    """启动sglang服务器"""
    from mineru.cli.vlm_sglang_server import main as sglang_main
    import sys
    
    # 设置启动参数
    sys.argv = [
        "sglang_server",
        "--host", "127.0.0.1",
        "--port", str(port),
    ]
    
    if model_path:
        sys.argv.extend(["--model-path", model_path])
    
    print(f"⚡ 启动sglang服务: http://127.0.0.1:{port}")
    sglang_main()

# 启动服务器
start_sglang_server()

# 客户端调用VLM后端
def use_vlm_backend():
    from mineru.cli.common import do_parse, read_fn
    from pathlib import Path
    
    do_parse(
        output_dir="./vlm_output",
        pdf_file_names=["demo"],
        pdf_bytes_list=[read_fn(Path("./demo.pdf"))],
        p_lang_list=["ch"],
        backend="vlm-sglang-client",  # 使用sglang客户端
        server_url="http://127.0.0.1:30000",  # sglang服务器地址
        formula_enable=True,
        table_enable=True,
    )

use_vlm_backend()
```

### 方式6: 多GPU高性能服务

**适用场景**: 大规模并行处理，充分利用多GPU资源

```python
def start_multi_gpu_server():
    """启动多GPU并行服务"""
    try:
        import litserve as ls
        from projects.multi_gpu_v2.server import MinerUAPI
        
        server = ls.LitServer(
            MinerUAPI(output_dir='/tmp/mineru_output'),
            accelerator='auto',      # 自动检测GPU
            devices='auto',          # 使用所有GPU
            workers_per_device=2,    # 每GPU工作进程数
            timeout=False           # 禁用超时
        )
        
        print("🔥 启动多GPU服务: http://127.0.0.1:8000")
        server.run(port=8000, generate_client_file=False)
        
    except ImportError:
        print("请安装: pip install litserve")

start_multi_gpu_server()
```

## ⚙️ 配置参数详解

### 后端选择 (backend)
- `pipeline`: 传统CV流水线，兼容性好，支持CPU
- `vlm-transformers`: HuggingFace transformers VLM
- `vlm-sglang-engine`: sglang引擎VLM，高性能
- `vlm-sglang-client`: sglang客户端，连接远程服务

### 语言支持 (lang)
- `ch`: 中文（默认）
- `en`: 英文
- `korean`: 韩文
- `japan`: 日文
- `arabic`: 阿拉伯文
- `latin`: 拉丁文
- 等84种语言...

### 解析方法 (parse_method)
- `auto`: 自动检测（推荐）
- `txt`: 文本提取模式
- `ocr`: OCR识别模式

### 功能开关
- `formula_enable`: 公式识别开关
- `table_enable`: 表格识别开关
- `start_page_id/end_page_id`: 页面范围控制

## 🔧 高级配置

### 设备和内存管理
```python
# 设置设备
os.environ['MINERU_DEVICE'] = 'cuda:0'  # 或 'cpu', 'mps'

# 设置显存限制
os.environ['MINERU_VRAM'] = '8'  # GB

# 设置模型源
os.environ['MINERU_MODEL_SOURCE'] = 'huggingface'  # 或 'modelscope'
```

### 自定义配置
```python
from mineru.utils.config_reader import read_config

# 读取配置
config = read_config()
if config:
    print("当前配置:", config)
```

## 🚨 常见问题

1. **模型下载失败**: 检查网络连接，尝试使用modelscope源
2. **显存不足**: 降低batch_size或使用CPU模式
3. **依赖冲突**: 使用虚拟环境隔离依赖
4. **权限问题**: 确保输出目录有写权限

## 📚 更多资源

- [官方文档](https://opendatalab.github.io/MinerU/)
- [GitHub仓库](https://github.com/opendatalab/MinerU)
- [在线体验](https://mineru.net/)
- [Discord社区](https://discord.gg/Tdedn9GTXq)
