#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MinerU 纯Python启动器
完全基于标准Python环境，无需uv框架
支持自动安装、模型下载、多种启动方式
"""

import os
import sys
import subprocess
import json
import asyncio
from pathlib import Path
from typing import List, Optional

class MinerUManager:
    """MinerU管理器 - 纯Python实现"""
    
    def __init__(self):
        self.python_exe = sys.executable
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志"""
        try:
            from loguru import logger
            self.logger = logger
        except ImportError:
            import logging
            logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
            self.logger = logging.getLogger(__name__)
    
    def log_info(self, msg: str):
        """记录信息日志"""
        if hasattr(self.logger, 'info'):
            self.logger.info(msg)
        else:
            self.logger.info(msg)
    
    def log_error(self, msg: str):
        """记录错误日志"""
        if hasattr(self.logger, 'error'):
            self.logger.error(msg)
        else:
            self.logger.error(msg)
    
    def run_command(self, cmd: List[str], capture_output: bool = True) -> bool:
        """执行命令"""
        try:
            if capture_output:
                result = subprocess.run(cmd, capture_output=True, text=True, check=True)
                return True
            else:
                subprocess.run(cmd, check=True)
                return True
        except subprocess.CalledProcessError as e:
            self.log_error(f"命令执行失败: {' '.join(cmd)}")
            if capture_output and e.stderr:
                self.log_error(f"错误信息: {e.stderr}")
            return False
        except Exception as e:
            self.log_error(f"执行出错: {e}")
            return False
    
    def check_python_version(self) -> bool:
        """检查Python版本"""
        version = sys.version_info
        if version.major < 3 or (version.major == 3 and version.minor < 8):
            self.log_error("❌ Python版本过低，需要Python 3.8+")
            return False
        
        self.log_info(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
        return True
    
    def install_mineru(self, full_install: bool = False) -> bool:
        """安装MinerU"""
        if not self.check_python_version():
            return False
        
        self.log_info("📦 开始安装MinerU...")
        
        # 升级pip
        self.log_info("⬆️ 升级pip...")
        if not self.run_command([self.python_exe, "-m", "pip", "install", "--upgrade", "pip"]):
            return False
        
        # 安装MinerU
        package = "mineru[all]" if full_install else "mineru[core]"
        self.log_info(f"📥 安装{package}...")
        
        if not self.run_command([self.python_exe, "-m", "pip", "install", package]):
            return False
        
        # 安装常用依赖
        optional_deps = ["fastapi", "uvicorn", "gradio"]
        for dep in optional_deps:
            self.log_info(f"📥 安装{dep}...")
            self.run_command([self.python_exe, "-m", "pip", "install", dep])
        
        self.log_info("✅ MinerU安装完成！")
        return True
    
    def check_installation(self) -> bool:
        """检查安装状态"""
        self.log_info("🔍 检查MinerU安装状态...")
        
        try:
            import mineru
            self.log_info(f"✅ MinerU已安装，版本: {mineru.version.__version__}")
        except ImportError:
            self.log_error("❌ MinerU未安装")
            return False
        
        # 检查可选依赖
        optional_deps = {
            'torch': 'GPU加速支持',
            'fastapi': 'API服务支持',
            'gradio': 'Web界面支持',
            'uvicorn': 'Web服务器支持'
        }
        
        for dep, desc in optional_deps.items():
            try:
                __import__(dep)
                self.log_info(f"✅ {desc}: 已安装")
            except ImportError:
                self.log_info(f"⚠️ {desc}: 未安装")
        
        return True
    
    def download_models(self, model_type: str = "all", source: str = "huggingface") -> bool:
        """下载模型"""
        self.log_info(f"📥 开始下载模型: {model_type} from {source}")
        
        cmd = [
            self.python_exe, "-m", "mineru.cli.models_download",
            "--model-type", model_type,
            "--source", source
        ]
        
        if self.run_command(cmd, capture_output=False):
            self.log_info("✅ 模型下载完成！")
            return True
        else:
            self.log_error("❌ 模型下载失败")
            return False
    
    def parse_pdf(self, pdf_path: str, output_dir: str = "./output", **kwargs) -> bool:
        """解析PDF文件"""
        try:
            from mineru.cli.common import do_parse, read_fn
            
            if not Path(pdf_path).exists():
                self.log_error(f"❌ 文件不存在: {pdf_path}")
                return False
            
            os.makedirs(output_dir, exist_ok=True)
            
            # 读取PDF
            pdf_bytes = read_fn(Path(pdf_path))
            file_name = Path(pdf_path).stem
            
            self.log_info(f"📄 开始解析: {pdf_path}")
            
            # 执行解析
            do_parse(
                output_dir=output_dir,
                pdf_file_names=[file_name],
                pdf_bytes_list=[pdf_bytes],
                p_lang_list=[kwargs.get('lang', 'ch')],
                backend=kwargs.get('backend', 'pipeline'),
                parse_method=kwargs.get('method', 'auto'),
                formula_enable=kwargs.get('formula', True),
                table_enable=kwargs.get('table', True),
                start_page_id=kwargs.get('start_page', 0),
                end_page_id=kwargs.get('end_page', None),
            )
            
            self.log_info(f"✅ 解析完成！结果保存在: {output_dir}")
            return True
            
        except ImportError:
            self.log_error("❌ MinerU未安装，请先运行安装")
            return False
        except Exception as e:
            self.log_error(f"❌ 解析失败: {e}")
            return False
    
    def start_api_server(self, host: str = "127.0.0.1", port: int = 8000) -> bool:
        """启动API服务"""
        try:
            import uvicorn
            
            self.log_info(f"🚀 启动API服务: http://{host}:{port}")
            self.log_info(f"📖 API文档: http://{host}:{port}/docs")
            self.log_info("💡 使用Ctrl+C停止服务")
            
            uvicorn.run(
                "mineru.cli.fast_api:app",
                host=host,
                port=port,
                reload=False
            )
            return True
            
        except ImportError:
            self.log_error("❌ 缺少依赖，请安装: pip install fastapi uvicorn")
            return False
        except Exception as e:
            self.log_error(f"❌ API服务启动失败: {e}")
            return False
    
    def start_gradio_app(self, host: str = "127.0.0.1", port: int = 7860) -> bool:
        """启动Gradio界面"""
        try:
            # 使用命令行方式启动Gradio
            cmd = [
                self.python_exe, "-m", "mineru.cli.gradio_app",
                "--host", host,
                "--port", str(port)
            ]
            
            self.log_info(f"🌐 启动Web界面: http://{host}:{port}")
            self.log_info("💡 使用Ctrl+C停止服务")
            
            return self.run_command(cmd, capture_output=False)
            
        except Exception as e:
            self.log_error(f"❌ Web界面启动失败: {e}")
            return False
    
    def interactive_menu(self):
        """交互式菜单"""
        while True:
            print("\n" + "="*50)
            print("🚀 MinerU 纯Python启动器")
            print("="*50)
            print("1. 检查安装状态")
            print("2. 安装MinerU (基础版)")
            print("3. 安装MinerU (完整版)")
            print("4. 下载模型")
            print("5. 解析PDF文件")
            print("6. 启动API服务")
            print("7. 启动Web界面")
            print("0. 退出")
            print("="*50)
            
            try:
                choice = input("请选择操作 (0-7): ").strip()
                
                if choice == "0":
                    self.log_info("👋 退出程序")
                    break
                    
                elif choice == "1":
                    self.check_installation()
                    
                elif choice == "2":
                    self.install_mineru(full_install=False)
                    
                elif choice == "3":
                    self.install_mineru(full_install=True)
                    
                elif choice == "4":
                    source = input("选择模型源 (huggingface/modelscope) [huggingface]: ").strip() or "huggingface"
                    self.download_models(source=source)
                    
                elif choice == "5":
                    pdf_path = input("请输入PDF文件路径: ").strip()
                    if pdf_path:
                        output_dir = input("输出目录 [./output]: ").strip() or "./output"
                        lang = input("语言 (ch/en/korean/japan) [ch]: ").strip() or "ch"
                        backend = input("后端 (pipeline/vlm-transformers) [pipeline]: ").strip() or "pipeline"
                        
                        self.parse_pdf(pdf_path, output_dir, lang=lang, backend=backend)
                    
                elif choice == "6":
                    host = input("服务器地址 [127.0.0.1]: ").strip() or "127.0.0.1"
                    port = input("端口 [8000]: ").strip() or "8000"
                    try:
                        port = int(port)
                        self.start_api_server(host, port)
                    except ValueError:
                        self.log_error("❌ 端口必须是数字")
                    
                elif choice == "7":
                    host = input("服务器地址 [127.0.0.1]: ").strip() or "127.0.0.1"
                    port = input("端口 [7860]: ").strip() or "7860"
                    try:
                        port = int(port)
                        self.start_gradio_app(host, port)
                    except ValueError:
                        self.log_error("❌ 端口必须是数字")
                    
                else:
                    self.log_error("⚠️ 无效选择，请重新输入")
                    
            except KeyboardInterrupt:
                self.log_info("\n👋 用户中断，退出程序")
                break
            except Exception as e:
                self.log_error(f"❌ 操作失败: {e}")


def main():
    """主函数"""
    manager = MinerUManager()
    
    if len(sys.argv) > 1:
        # 命令行模式
        command = sys.argv[1]
        
        if command == "install":
            full = "--full" in sys.argv
            manager.install_mineru(full_install=full)
            
        elif command == "check":
            manager.check_installation()
            
        elif command == "download":
            manager.download_models()
            
        elif command == "parse" and len(sys.argv) > 2:
            pdf_path = sys.argv[2]
            manager.parse_pdf(pdf_path)
            
        elif command == "api":
            manager.start_api_server()
            
        elif command == "web":
            manager.start_gradio_app()
            
        else:
            print("用法:")
            print("  python pure_python_mineru.py install [--full]  # 安装MinerU")
            print("  python pure_python_mineru.py check             # 检查安装")
            print("  python pure_python_mineru.py download          # 下载模型")
            print("  python pure_python_mineru.py parse <pdf_path>  # 解析PDF")
            print("  python pure_python_mineru.py api               # 启动API服务")
            print("  python pure_python_mineru.py web               # 启动Web界面")
            print("  python pure_python_mineru.py                   # 交互式菜单")
    else:
        # 交互式模式
        manager.interactive_menu()


if __name__ == "__main__":
    main()
